# 🤖 Spring AI Complete Guide - Functions, Purpose & Usage

## 📋 **Overview**

This comprehensive guide explains every Spring AI function used in the MCP system, their purposes, and detailed usage instructions with practical examples.

## 🏗️ **Spring AI Architecture in MCP System**

```
┌─────────────────────────────────────────────────────────────┐
│                    Spring AI Layer                          │
├─────────────────────────────────────────────────────────────┤
│  ChatClient  │  @Tool Functions  │  OpenAI Integration     │
│              │                   │                         │
│  • Auto Tool │  • @Tool          │  • GPT-4o Model        │
│    Selection │  • Descriptions   │  • Function Calling    │
│  • Prompt    │  • Parameters     │  • Natural Language    │
│    Management│  • Return Types   │    Understanding       │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 **Core Spring AI Components**

### **1. ChatClient - The Main Interface**

#### **Purpose:**
ChatClient is the primary interface for interacting with AI models. It handles:
- **Prompt management** and conversation flow
- **Automatic tool detection** and calling
- **Response generation** and formatting
- **Context management** across conversations

#### **Configuration:**
**Location:** `SpringAiToolConfig.java`

```java
@Bean
public ChatClient winMcpChatClient() {
    return ChatClient.builder(openAiChatModel)
            .defaultSystem("""
                You are a helpful pharmacy system assistant for WinPlus pharmacy management system.
                You have access to specialized tools to retrieve different types of data for users.
                
                When a user asks a question, analyze their request and automatically call the most 
                appropriate tool to get the requested information. Always provide a helpful and 
                detailed response based on the tool results.
                
                Available tools:
                - Client data tool: For questions about client information, customer details, profiles
                - Sales data tool: For questions about sales, revenue, transactions, invoices, billing
                - Product data tool: For questions about products, inventory, stock, medications
                - Purchase data tool: For questions about purchases, suppliers, procurement
                
                Respond in French when appropriate, and always be helpful and professional.
                """)
            .defaultTools(winMcpSpecificTools) // Auto-detects @Tool methods
            .build();
}
```

#### **Usage Examples:**

**Basic Usage:**
```java
// Simple question-answer
String response = winMcpChatClient
    .prompt("What are my sales this month?")
    .call()
    .content();
```

**With Context:**
```java
// Adding user context
String userPrompt = String.format("""
    User: %s (username: %s)
    
    Please help me with this request by using the appropriate tool to get the information I need.
    """, userMessage, username);

String response = winMcpChatClient
    .prompt(userPrompt)
    .call()
    .content();
```

**With Custom Options:**
```java
// Custom temperature and max tokens
String response = winMcpChatClient
    .prompt("Show me my client information")
    .options(OpenAiChatOptions.builder()
        .withTemperature(0.3f)
        .withMaxTokens(1500)
        .build())
    .call()
    .content();
```

### **2. @Tool Annotation - Function Definition**

#### **Purpose:**
The @Tool annotation marks methods as AI-callable functions. It provides:
- **Function description** for AI understanding
- **Automatic parameter schema** generation
- **Return type handling** and conversion
- **Integration with ChatClient** for automatic calling

#### **Syntax and Parameters:**

```java
@Tool(
    description = "Detailed description of what this tool does and when to use it",
    name = "optionalCustomName",           // Optional: defaults to method name
    returnDirect = false,                  // Optional: return result directly to user
    resultConverter = CustomConverter.class // Optional: custom result converter
)
public ReturnType methodName(ParameterType parameter) {
    // Implementation
}
```

#### **Detailed Examples:**

**Basic @Tool Function:**
```java
@Tool(description = "Get current date and time in user's timezone")
public String getCurrentDateTime() {
    return LocalDateTime.now().toString();
}
```

**@Tool with Parameters:**
```java
@Tool(description = "Get client and customer information from WinPlus pharmacy system. Use this tool when users ask about their client information, customer details, personal profiles, account information, customer management, or any questions related to client data.")
public String getWinMcpClientData(WinMcpDataRequest request) {
    try {
        // Authentication
        WinMcpAuthService.WinMcpAuthResult authResult = authenticateUser(request.username());
        
        // API call
        WebClient webClient = createAuthenticatedWebClient(authResult);
        Map<String, Object> clientData = webClient.get()
                .uri("/api/winplus/clients/code/" + request.username())
                .retrieve()
                .bodyToMono(Map.class)
                .block();
        
        // Format response
        return formatClientData(clientData, request.username());
    } catch (Exception e) {
        return "Error retrieving client data: " + e.getMessage();
    }
}
```

**@Tool with Complex Parameters:**
```java
@Tool(description = "Search products by criteria including name, category, and price range")
public String searchProducts(
    @ToolParam(description = "Product name or partial name to search for") String productName,
    @ToolParam(description = "Product category", required = false) String category,
    @ToolParam(description = "Minimum price", required = false) Double minPrice,
    @ToolParam(description = "Maximum price", required = false) Double maxPrice
) {
    // Implementation with optional parameters
}
```

### **3. @ToolParam Annotation - Parameter Definition**

#### **Purpose:**
@ToolParam provides detailed information about function parameters to help AI understand:
- **Parameter purpose** and expected format
- **Required vs optional** parameters
- **Value constraints** and examples

#### **Syntax:**
```java
@ToolParam(
    description = "Detailed description of the parameter",
    required = true,  // Optional: defaults to true
    example = "example value" // Optional: example for AI
)
ParameterType parameterName
```

#### **Examples:**

**Required Parameter:**
```java
@Tool(description = "Get sales data for specific time period")
public String getSalesData(
    @ToolParam(description = "Username of the client") String username,
    @ToolParam(description = "Start date in ISO format (YYYY-MM-DD)") String startDate,
    @ToolParam(description = "End date in ISO format (YYYY-MM-DD)") String endDate
) {
    // Implementation
}
```

**Optional Parameters:**
```java
@Tool(description = "Get product information with optional filters")
public String getProducts(
    @ToolParam(description = "Username of the client") String username,
    @ToolParam(description = "Product category filter", required = false) String category,
    @ToolParam(description = "Maximum number of results", required = false) Integer limit
) {
    // Implementation
}
```

**Complex Parameter Types:**
```java
// Custom request object
@JsonClassDescription("Request for retrieving user-specific data from WinPlus system")
public record WinMcpDataRequest(
    @JsonPropertyDescription("The username of the user to get data for") 
    String username,
    
    @JsonPropertyDescription("Optional date filter in ISO format") 
    String dateFilter,
    
    @JsonPropertyDescription("Optional limit for number of results") 
    Integer limit
) {}

@Tool(description = "Get comprehensive user data")
public String getUserData(WinMcpDataRequest request) {
    // Implementation
}
```

### **4. OpenAI Integration - Model Configuration**

#### **Purpose:**
Configures the underlying OpenAI model for:
- **Model selection** (GPT-4o, GPT-3.5, etc.)
- **Response parameters** (temperature, max tokens)
- **Function calling** capabilities
- **API authentication** and rate limiting

#### **Configuration:**
**Location:** `application.properties`

```properties
# OpenAI Model Configuration
spring.ai.openai.api-key=your-api-key-here
spring.ai.openai.model=gpt-4o
spring.ai.openai.temperature=0.7
spring.ai.openai.max-tokens=2000
spring.ai.openai.timeout=60

# Legacy OpenAI Service Configuration (for compatibility)
openai.api-key=your-api-key-here
openai.model=gpt-4o
openai.temperature=0.7
openai.max-tokens=2000
openai.timeout=60
```

#### **Bean Configuration:**
```java
@Configuration
public class OpenAiConfig {
    
    @Bean
    public OpenAiChatModel openAiChatModel(
        @Value("${spring.ai.openai.api-key}") String apiKey,
        @Value("${spring.ai.openai.model}") String model
    ) {
        return OpenAiChatModel.builder()
            .apiKey(apiKey)
            .model(model)
            .temperature(0.7f)
            .maxTokens(2000)
            .build();
    }
}
```

### **5. Tool Context - Passing Additional Data**

#### **Purpose:**
ToolContext allows passing additional contextual information to tools that isn't part of the AI conversation:
- **User session data** (tenant ID, permissions)
- **Request metadata** (IP address, timestamp)
- **Configuration parameters** (environment, feature flags)

#### **Usage:**

**Defining Tool with Context:**
```java
@Tool(description = "Get user-specific data with tenant isolation")
public String getUserDataWithContext(
    @ToolParam(description = "Username") String username,
    ToolContext toolContext
) {
    // Access context data
    String tenantId = toolContext.get("tenantId");
    String userRole = toolContext.get("userRole");
    
    // Use context in implementation
    return fetchUserData(username, tenantId, userRole);
}
```

**Providing Context:**
```java
// In service method
String response = winMcpChatClient
    .prompt("Get my user information")
    .toolContext(Map.of(
        "tenantId", "pharmacy_001",
        "userRole", "pharmacist",
        "requestId", UUID.randomUUID().toString()
    ))
    .call()
    .content();
```

### **6. Function Calling Process - How It Works**

#### **Step-by-Step Process:**

**1. Question Analysis:**
```java
// User asks: "What are my sales this month?"
// Spring AI analyzes the question and determines intent
```

**2. Tool Matching:**
```java
// AI matches question with @Tool descriptions:
// "sales" → matches "Get sales and revenue data..." description
// Selects: getWinMcpSalesData()
```

**3. Parameter Extraction:**
```java
// AI extracts parameters from context:
// username: from user context
// Creates: WinMcpDataRequest("user123")
```

**4. Function Execution:**
```java
// Spring AI calls the selected tool:
String result = winMcpSpecificTools.getWinMcpSalesData(
    new WinMcpDataRequest("user123")
);
```

**5. Response Generation:**
```java
// AI uses tool result to generate final response:
// Combines tool output with natural language explanation
```

### **7. Error Handling and Fallbacks**

#### **Tool-Level Error Handling:**
```java
@Tool(description = "Get client data with error handling")
public String getClientDataSafe(WinMcpDataRequest request) {
    try {
        // Main implementation
        return getClientData(request);
    } catch (AuthenticationException e) {
        return "Authentication failed. Please check your credentials.";
    } catch (NetworkException e) {
        return "Network error. Please try again later.";
    } catch (Exception e) {
        return "An unexpected error occurred: " + e.getMessage();
    }
}
```

#### **ChatClient Error Handling:**
```java
public Mono<String> getDataWithFallback(String username, String userMessage) {
    return Mono.fromCallable(() -> {
        try {
            return winMcpChatClient
                .prompt(userMessage + " (username: " + username + ")")
                .call()
                .content();
        } catch (Exception e) {
            // Fallback to direct tool call
            return winMcpSpecificTools.getWinMcpClientData(
                new WinMcpDataRequest(username)
            );
        }
    });
}
```

### **8. Performance Optimization**

#### **Caching Strategies:**
```java
@Service
public class CachedToolService {
    
    private final Cache<String, String> responseCache = 
        Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(5))
            .build();
    
    @Tool(description = "Get cached client data")
    public String getCachedClientData(WinMcpDataRequest request) {
        String cacheKey = "client_" + request.username();
        
        return responseCache.get(cacheKey, key -> {
            // Expensive operation only if not cached
            return fetchClientDataFromBackend(request);
        });
    }
}
```

#### **Async Processing:**
```java
@Tool(description = "Get data asynchronously")
public CompletableFuture<String> getDataAsync(WinMcpDataRequest request) {
    return CompletableFuture.supplyAsync(() -> {
        // Long-running operation
        return fetchLargeDataset(request);
    });
}
```

## 🎯 **Best Practices**

### **1. Tool Description Writing:**
```java
// ❌ Poor description
@Tool(description = "Gets data")

// ✅ Good description  
@Tool(description = "Get comprehensive sales and revenue data from WinPlus pharmacy system. Use this tool when users ask about sales performance, revenue analysis, transaction history, invoices, billing information, financial reports, or any questions related to sales and financial data. Supports French terms like 'ventes', 'chiffre d'affaires', 'factures'.")
```

### **2. Parameter Validation:**
```java
@Tool(description = "Get sales data with validation")
public String getSalesData(
    @ToolParam(description = "Username (required, 3-50 characters)") String username,
    @ToolParam(description = "Date range in days (1-365)", required = false) Integer days
) {
    // Validate inputs
    if (username == null || username.trim().isEmpty()) {
        return "Error: Username is required";
    }
    if (days != null && (days < 1 || days > 365)) {
        return "Error: Date range must be between 1 and 365 days";
    }
    
    // Implementation
}
```

### **3. Response Formatting:**
```java
private String formatResponse(Map<String, Object> data, String username) {
    StringBuilder sb = new StringBuilder();
    sb.append("=== 📊 SALES REPORT FOR ").append(username.toUpperCase()).append(" ===\n\n");
    
    // Add structured data
    if (data.containsKey("totalSales")) {
        sb.append("💰 Total Sales: ").append(data.get("totalSales")).append(" DH\n");
    }
    
    // Add summary
    sb.append("\n📋 Summary: ").append(generateSummary(data)).append("\n");
    
    return sb.toString();
}
```

## 🚀 **Advanced Usage Patterns**

### **1. Conditional Tool Execution:**
```java
@Tool(description = "Get data based on user permissions")
public String getDataByPermission(WinMcpDataRequest request, ToolContext context) {
    String userRole = context.get("userRole");
    
    return switch (userRole) {
        case "admin" -> getFullData(request);
        case "pharmacist" -> getPharmacistData(request);
        case "user" -> getBasicData(request);
        default -> "Access denied: insufficient permissions";
    };
}
```

### **2. Multi-Step Tool Chains:**
```java
@Tool(description = "Get comprehensive business report")
public String getBusinessReport(WinMcpDataRequest request) {
    // Step 1: Get client data
    String clientData = getWinMcpClientData(request);
    
    // Step 2: Get sales data
    String salesData = getWinMcpSalesData(request);
    
    // Step 3: Get product data
    String productData = getWinMcpProductData(request);
    
    // Step 4: Combine and analyze
    return generateComprehensiveReport(clientData, salesData, productData);
}
```

## 🔧 **Configuration Deep Dive**

### **1. Application Properties Configuration**
```properties
# ==================== SPRING AI CONFIGURATION ====================

# OpenAI API Configuration
spring.ai.openai.api-key=sk-proj-your-api-key-here
spring.ai.openai.model=gpt-4o                    # Model selection
spring.ai.openai.temperature=0.7                 # Creativity level (0.0-2.0)
spring.ai.openai.max-tokens=2000                 # Response length limit
spring.ai.openai.timeout=60                      # Request timeout in seconds

# Tool Strategy Configuration
mcp.tool.strategy=SPECIFIC                       # SPECIFIC or SMART
mcp.backend.type=WIN_MCP                         # WIN_MCP or CHAT_MCP

# Backend URLs
api.urls.win-mcp.base-url=http://localhost:8082
api.urls.chat-mcp.base-url=http://localhost:8080

# Authentication Configuration
win-mcp.tenant.username=0001
win-mcp.tenant.password=123456
```

### **2. Bean Configuration Examples**
```java
@Configuration
@EnableConfigurationProperties
public class SpringAiConfiguration {

    @Bean
    @Primary
    public ChatClient primaryChatClient(OpenAiChatModel chatModel) {
        return ChatClient.builder(chatModel)
            .defaultSystem("You are a helpful AI assistant.")
            .build();
    }

    @Bean("winMcpChatClient")
    public ChatClient winMcpChatClient(
        OpenAiChatModel chatModel,
        WinMcpSpecificToolsFunctions toolsFunctions
    ) {
        return ChatClient.builder(chatModel)
            .defaultSystem("""
                You are a pharmacy system assistant for WinPlus.
                Analyze user questions and call appropriate tools.
                Always respond in French when appropriate.
                """)
            .defaultTools(toolsFunctions)
            .defaultOptions(OpenAiChatOptions.builder()
                .withTemperature(0.3f)  // Lower temperature for more consistent responses
                .withMaxTokens(1500)
                .build())
            .build();
    }
}
```

## 🎭 **Advanced Tool Patterns**

### **1. Conditional Tool Selection**
```java
@Component
public class ConditionalToolService {

    @Tool(description = "Get user data with role-based access control")
    public String getUserDataByRole(
        @ToolParam(description = "Username") String username,
        ToolContext context
    ) {
        String userRole = context.get("userRole");
        String tenantId = context.get("tenantId");

        // Role-based data access
        return switch (userRole.toLowerCase()) {
            case "admin" -> getAdminData(username, tenantId);
            case "pharmacist" -> getPharmacistData(username, tenantId);
            case "customer" -> getCustomerData(username, tenantId);
            default -> "Access denied: Unknown role " + userRole;
        };
    }

    @Tool(description = "Get data based on business hours")
    public String getDataByBusinessHours(WinMcpDataRequest request) {
        LocalTime now = LocalTime.now();
        LocalTime businessStart = LocalTime.of(8, 0);
        LocalTime businessEnd = LocalTime.of(18, 0);

        if (now.isBefore(businessStart) || now.isAfter(businessEnd)) {
            return "Pharmacy is currently closed. Limited data available.";
        }

        return getFullBusinessData(request);
    }
}
```

### **2. Data Aggregation Tools**
```java
@Tool(description = "Generate comprehensive business dashboard with all key metrics")
public String getBusinessDashboard(WinMcpDataRequest request) {
    try {
        // Parallel data fetching
        CompletableFuture<String> clientsFuture = CompletableFuture
            .supplyAsync(() -> getWinMcpClientData(request));

        CompletableFuture<String> salesFuture = CompletableFuture
            .supplyAsync(() -> getWinMcpSalesData(request));

        CompletableFuture<String> productsFuture = CompletableFuture
            .supplyAsync(() -> getWinMcpProductData(request));

        CompletableFuture<String> purchasesFuture = CompletableFuture
            .supplyAsync(() -> getWinMcpPurchaseData(request));

        // Wait for all to complete
        CompletableFuture.allOf(clientsFuture, salesFuture, productsFuture, purchasesFuture)
            .join();

        // Combine results
        return formatDashboard(
            clientsFuture.get(),
            salesFuture.get(),
            productsFuture.get(),
            purchasesFuture.get()
        );

    } catch (Exception e) {
        return "Error generating dashboard: " + e.getMessage();
    }
}
```

### **3. Streaming Response Tools**
```java
@Tool(description = "Get large dataset with streaming response")
public Flux<String> getLargeDatasetStream(WinMcpDataRequest request) {
    return Flux.fromIterable(fetchLargeDataset(request))
        .map(this::formatDataChunk)
        .delayElements(Duration.ofMillis(100)); // Throttle for better UX
}
```

## 🔍 **Debugging and Monitoring**

### **1. Tool Execution Logging**
```java
@Aspect
@Component
public class ToolExecutionLogger {

    @Around("@annotation(tool)")
    public Object logToolExecution(ProceedingJoinPoint joinPoint, Tool tool) throws Throwable {
        String toolName = tool.name().isEmpty() ? joinPoint.getSignature().getName() : tool.name();
        Object[] args = joinPoint.getArgs();

        long startTime = System.currentTimeMillis();

        try {
            System.out.println("🔧 Executing tool: " + toolName + " with args: " + Arrays.toString(args));

            Object result = joinPoint.proceed();

            long executionTime = System.currentTimeMillis() - startTime;
            System.out.println("✅ Tool " + toolName + " completed in " + executionTime + "ms");

            return result;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            System.out.println("❌ Tool " + toolName + " failed after " + executionTime + "ms: " + e.getMessage());
            throw e;
        }
    }
}
```

### **2. Performance Monitoring**
```java
@Component
public class ToolPerformanceMonitor {

    private final MeterRegistry meterRegistry;
    private final Map<String, Timer> toolTimers = new ConcurrentHashMap<>();

    public ToolPerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public <T> T measureToolExecution(String toolName, Supplier<T> toolExecution) {
        Timer timer = toolTimers.computeIfAbsent(toolName,
            name -> Timer.builder("tool.execution.time")
                .tag("tool", name)
                .register(meterRegistry));

        return timer.recordCallable(toolExecution::get);
    }
}
```

## 🚨 **Error Handling Strategies**

### **1. Graceful Degradation**
```java
@Tool(description = "Get sales data with fallback options")
public String getSalesDataWithFallback(WinMcpDataRequest request) {
    try {
        // Try primary data source
        return getPrimarySalesData(request);
    } catch (PrimaryServiceException e) {
        try {
            // Fallback to cached data
            return getCachedSalesData(request);
        } catch (CacheException ce) {
            // Final fallback to basic data
            return getBasicSalesData(request);
        }
    }
}
```

### **2. Circuit Breaker Pattern**
```java
@Component
public class CircuitBreakerToolService {

    private final CircuitBreaker circuitBreaker;

    public CircuitBreakerToolService() {
        this.circuitBreaker = CircuitBreaker.ofDefaults("winMcpService");
        circuitBreaker.getEventPublisher()
            .onStateTransition(event ->
                System.out.println("Circuit breaker state transition: " + event));
    }

    @Tool(description = "Get data with circuit breaker protection")
    public String getDataWithCircuitBreaker(WinMcpDataRequest request) {
        return circuitBreaker.executeSupplier(() -> {
            return callExternalService(request);
        });
    }
}
```

## 📊 **Testing Spring AI Tools**

### **1. Unit Testing Tools**
```java
@ExtendWith(MockitoExtension.class)
class WinMcpToolsTest {

    @Mock
    private WinMcpAuthService authService;

    @Mock
    private WebClient webClient;

    @InjectMocks
    private WinMcpSpecificToolsFunctions toolsFunctions;

    @Test
    void testGetClientData_Success() {
        // Given
        WinMcpDataRequest request = new WinMcpDataRequest("testuser");
        WinMcpAuthService.WinMcpAuthResult authResult = createMockAuthResult();

        when(authService.authenticateUser("testuser")).thenReturn(Mono.just(authResult));

        // When
        String result = toolsFunctions.getWinMcpClientData(request);

        // Then
        assertThat(result).contains("CLIENT DATA");
        verify(authService).authenticateUser("testuser");
    }

    @Test
    void testGetClientData_AuthenticationFailure() {
        // Given
        WinMcpDataRequest request = new WinMcpDataRequest("testuser");
        when(authService.authenticateUser("testuser")).thenReturn(Mono.empty());

        // When
        String result = toolsFunctions.getWinMcpClientData(request);

        // Then
        assertThat(result).contains("Erreur d'authentification");
    }
}
```

### **2. Integration Testing**
```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.ai.openai.api-key=test-key",
    "mcp.tool.strategy=SPECIFIC"
})
class SpringAiIntegrationTest {

    @Autowired
    private ChatClient winMcpChatClient;

    @MockBean
    private WinMcpSpecificToolsFunctions toolsFunctions;

    @Test
    void testChatClientWithTools() {
        // Given
        when(toolsFunctions.getWinMcpSalesData(any()))
            .thenReturn("Mock sales data");

        // When
        String response = winMcpChatClient
            .prompt("What are my sales?")
            .call()
            .content();

        // Then
        assertThat(response).isNotEmpty();
        verify(toolsFunctions).getWinMcpSalesData(any());
    }
}
```

## 🎯 **Summary**

This comprehensive guide covers:

1. **Core Components** - ChatClient, @Tool, @ToolParam, OpenAI integration
2. **Configuration** - Properties, beans, and advanced setup
3. **Advanced Patterns** - Conditional tools, aggregation, streaming
4. **Monitoring** - Logging, performance tracking, debugging
5. **Error Handling** - Graceful degradation, circuit breakers
6. **Testing** - Unit tests, integration tests, mocking strategies

Spring AI in the MCP system provides intelligent, flexible, and maintainable tool selection that eliminates hardcoded logic while delivering high performance and natural language understanding.


--------------------------
--------------------------

Based on the Spring AI function calling process, here are the specific functions responsible for each step:

🔄 Function Calling Process - Responsible Functions

** Step 1:**  Question Analysis
Responsible Function:

ChatClient.prompt(userMessage) - Receives and analyzes the user question
Internal OpenAI GPT-4o model processes natural language understanding

** Step 2:**  Tool Matching
Responsible Function:

ChatClient.defaultTools(winMcpSpecificTools) - Provides available tools
@Tool(description = "Get sales and revenue data...") - Tool descriptions for matching
Internal Spring AI tool resolution mechanism

** Step 3:**   Parameter Extraction
Responsible Function:

@ToolParam annotations - Define parameter requirements
WinMcpDataRequest record - Parameter structure
Spring AI's automatic parameter mapping from context

** Step 4:**   Function Execution
Responsible Function:

getWinMcpSalesData(WinMcpDataRequest request) - The actual @Tool method
authenticateUser(request.username()) - Authentication
createAuthenticatedWebClient(authResult) - WebClient creation
Backend API calls to Win-MCP endpoints

** Step 5:**   Response Generation
Responsible Function:

formatSalesData(salesStats, clientSalesData, username) - Data formatting
ChatClient.call().content() - Final response generation
OpenAI model combines tool result with natural language


